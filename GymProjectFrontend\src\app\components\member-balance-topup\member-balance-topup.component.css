/* Member Balance Top-up Component Styles */

/* Search Container */
.search-container {
  flex: 1;
  max-width: 300px;
}

.search-input {
  padding-right: 40px;
  border-radius: 25px;
  border: 2px solid var(--border-color);
  transition: all 0.3s ease;
  background: var(--bg-secondary);
}

.search-input:focus {
  border-color: var(--primary);
  box-shadow: 0 0 0 3px rgba(var(--primary-rgb), 0.1);
  background: white;
}

.search-icon {
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-secondary);
  transition: color 0.3s ease;
}

.search-input:focus + .search-icon {
  color: var(--primary);
}

/* Filter Info */
.filter-info {
  display: flex;
  align-items: center;
}

.filter-count {
  background: var(--primary);
  color: white;
  padding: 6px 12px;
  border-radius: 15px;
  font-size: 0.85rem;
  font-weight: 500;
  box-shadow: 0 2px 4px rgba(var(--primary-rgb), 0.3);
}

/* Balance Filter Container */
.balance-filter-container {
  margin-top: 0.5rem;
}

.balance-filter-buttons {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

/* Balance Filter Buttons */
.balance-filter-btn {
  background: white;
  border: 2px solid var(--border-color);
  border-radius: 12px;
  padding: 0;
  cursor: pointer;
  transition: all 0.3s ease;
  flex: 1;
  min-width: 140px;
  position: relative;
  overflow: hidden;
}

.balance-filter-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-color: var(--primary);
}

.balance-filter-btn.active {
  border-color: var(--primary);
  background: linear-gradient(135deg, var(--primary), var(--primary-dark));
  color: white;
  box-shadow: 0 4px 15px rgba(var(--primary-rgb), 0.3);
}

.balance-filter-btn.positive:hover:not(.active) {
  border-color: var(--success);
}

.balance-filter-btn.positive.active {
  background: linear-gradient(135deg, var(--success), #1e7e34);
  border-color: var(--success);
}

.balance-filter-btn.negative:hover:not(.active) {
  border-color: var(--danger);
}

.balance-filter-btn.negative.active {
  background: linear-gradient(135deg, var(--danger), #c82333);
  border-color: var(--danger);
}

/* Filter Button Content */
.filter-btn-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 16px 12px;
  gap: 8px;
}

.filter-btn-icon {
  font-size: 1.2rem;
  color: var(--text-secondary);
  transition: all 0.3s ease;
}

.balance-filter-btn:hover .filter-btn-icon {
  color: var(--primary);
  transform: scale(1.1);
}

.balance-filter-btn.active .filter-btn-icon {
  color: white;
  transform: scale(1.1);
}

.balance-filter-btn.positive:hover:not(.active) .filter-btn-icon {
  color: var(--success);
}

.balance-filter-btn.negative:hover:not(.active) .filter-btn-icon {
  color: var(--danger);
}

.filter-btn-text {
  font-size: 0.9rem;
  font-weight: 500;
  color: var(--text-primary);
  transition: color 0.3s ease;
}

.balance-filter-btn.active .filter-btn-text {
  color: white;
}

.filter-btn-count {
  background: var(--bg-tertiary);
  color: var(--text-secondary);
  padding: 4px 8px;
  border-radius: 10px;
  font-size: 0.8rem;
  font-weight: 600;
  min-width: 24px;
  text-align: center;
  transition: all 0.3s ease;
}

.balance-filter-btn.active .filter-btn-count {
  background: rgba(255, 255, 255, 0.2);
  color: white;
}

/* Responsive Design */
@media (max-width: 768px) {
  .balance-filter-buttons {
    flex-direction: column;
  }

  .balance-filter-btn {
    min-width: 100%;
  }

  .filter-btn-content {
    flex-direction: row;
    justify-content: space-between;
    padding: 12px 16px;
  }

  .filter-btn-icon {
    order: 1;
  }

  .filter-btn-text {
    order: 2;
    flex: 1;
    text-align: left;
    margin-left: 12px;
  }

  .filter-btn-count {
    order: 3;
  }

  .search-container {
    max-width: 100%;
    margin-bottom: 10px;
  }
}