/* Member Balance Top-up Component Styles */

/* Modern Filter Button */
.modern-filter-btn {
  background: linear-gradient(135deg, var(--primary), var(--primary-dark));
  border: none;
  border-radius: 12px;
  color: white;
  padding: 10px 16px;
  font-weight: 500;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  box-shadow: 0 4px 12px rgba(var(--primary-rgb), 0.3);
  position: relative;
  overflow: hidden;
}

.modern-filter-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(var(--primary-rgb), 0.4);
  background: linear-gradient(135deg, var(--primary-dark), var(--primary));
}

.modern-filter-btn:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(var(--primary-rgb), 0.3);
}

.modern-filter-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.modern-filter-btn:hover::before {
  left: 100%;
}

.filter-text {
  font-size: 0.9rem;
  font-weight: 500;
}

.filter-badge {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  padding: 2px 8px;
  border-radius: 10px;
  font-size: 0.8rem;
  font-weight: 600;
  margin-left: 8px;
}

/* Modern Dropdown Menu */
.modern-dropdown-menu {
  border: none;
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
  padding: 8px;
  margin-top: 8px;
  background: white;
  min-width: 220px;
}

.modern-dropdown-item {
  border-radius: 8px;
  padding: 12px 16px;
  margin-bottom: 4px;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: var(--text-primary);
  text-decoration: none;
  position: relative;
  overflow: hidden;
}

.modern-dropdown-item:last-child {
  margin-bottom: 0;
}

.modern-dropdown-item:hover {
  background: var(--bg-secondary);
  color: var(--primary);
  transform: translateX(4px);
}

.modern-dropdown-item.active {
  background: linear-gradient(135deg, var(--primary), var(--primary-dark));
  color: white;
  box-shadow: 0 4px 12px rgba(var(--primary-rgb), 0.3);
}

.modern-dropdown-item.active:hover {
  transform: translateX(0);
  background: linear-gradient(135deg, var(--primary-dark), var(--primary));
}

.item-count {
  background: var(--bg-tertiary);
  color: var(--text-secondary);
  padding: 4px 8px;
  border-radius: 10px;
  font-size: 0.8rem;
  font-weight: 600;
  min-width: 24px;
  text-align: center;
  transition: all 0.3s ease;
}

.modern-dropdown-item.active .item-count {
  background: rgba(255, 255, 255, 0.2);
  color: white;
}

.modern-dropdown-item:hover:not(.active) .item-count {
  background: var(--primary);
  color: white;
}

/* Dropdown Animation */
.modern-dropdown-menu {
  animation: dropdownFadeIn 0.3s ease;
}

@keyframes dropdownFadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}