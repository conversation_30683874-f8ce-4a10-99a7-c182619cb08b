<div class="container mt-4">
  <div *ngIf="isLoading" class="d-flex justify-content-center align-items-center" style="height: 100vh;">
    <app-loading-spinner></app-loading-spinner>
  </div>
  
  <div [class.content-blur]="isLoading" class="fade-in">
    <div class="row">
      <!-- Ba<PERSON>ye İstatistikleri -->
      <div class="col-md-12 mb-4">
        <div class="modern-card">
          <div class="modern-card-header">
            <h5>Ba<PERSON>ye İstatistikleri</h5>
            <div>
              
            </div>
          </div>
          <div class="modern-card-body">
            <div class="row">
              <div class="col-md-3 col-sm-6 mb-3">
                <div class="modern-stats-card" style="background-color: var(--primary-light);">
                  <div class="modern-stats-icon" style="background-color: var(--primary);">
                    <i class="fas fa-users"></i>
                  </div>
                  <div class="modern-stats-info">
                    <div class="modern-stats-value">{{membersWithBalance.length}}</div>
                    <div class="modern-stats-label">Bakiyeli Üye</div>
                  </div>
                </div>
              </div>
              <div class="col-md-3 col-sm-6 mb-3">
                <div class="modern-stats-card" style="background-color: var(--success-light);">
                  <div class="modern-stats-icon" style="background-color: var(--success);">
                    <i class="fas fa-wallet"></i>
                  </div>
                  <div class="modern-stats-info">
                    <div class="modern-stats-value">{{getTotalPositiveBalance() | currency:'TRY':'symbol-narrow':'1.2-2'}}</div>
                    <div class="modern-stats-label">Toplam Pozitif Bakiye</div>
                  </div>
                </div>
              </div>
              <div class="col-md-3 col-sm-6 mb-3">
                <div class="modern-stats-card" style="background-color: var(--danger-light);">
                  <div class="modern-stats-icon" style="background-color: var(--danger);">
                    <i class="fas fa-exclamation-circle"></i>
                  </div>
                  <div class="modern-stats-info">
                    <div class="modern-stats-value">{{getTotalNegativeBalance() | currency:'TRY':'symbol-narrow':'1.2-2'}}</div>
                    <div class="modern-stats-label">Toplam Negatif Bakiye</div>
                  </div>
                </div>
              </div>
              <div class="col-md-3 col-sm-6 mb-3">
                <div class="modern-stats-card" style="background-color: var(--info-light);">
                  <div class="modern-stats-icon" style="background-color: var(--info);">
                    <i class="fas fa-chart-line"></i>
                  </div>
                  <div class="modern-stats-info">
                    <div class="modern-stats-value">{{getNetBalance() | currency:'TRY':'symbol-narrow':'1.2-2'}}</div>
                    <div class="modern-stats-label">Net Bakiye</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Bakiye Yükleme Formu -->
      <div class="col-md-4">
        <div class="modern-card slide-in-left">
          <div class="modern-card-header">
            <h5>Bakiye Yükleme</h5>
          </div>
          <div class="modern-card-body">
            <form [formGroup]="topupForm" (ngSubmit)="topup()">
              <div class="modern-form-group">
                <label for="memberSelect" class="modern-form-label">Üye Seçin</label>
                <div class="input-group">
                  <div class="input-group-text">
                    <i class="fas fa-user"></i>
                  </div>
                  <input id="memberSelect" type="text" class="modern-form-control" [formControl]="memberControl" [matAutocomplete]="auto" placeholder="Üye adı veya telefon" style="width: 70%;">
                </div>
                <mat-autocomplete #auto="matAutocomplete" [displayWith]="displayMember">
                  <mat-option *ngFor="let member of filteredMembers | async" [value]="member">
                    {{member.name}} - {{member.phoneNumber}}
                  </mat-option>
                </mat-autocomplete>
              </div>
              <div class="modern-form-group">
                <label for="amount" class="modern-form-label">Yüklenecek TL</label>
                <div class="input-group">
                  <div class="input-group-text">
                    <i class="fas fa-money-bill"></i>
                  </div>
                  <input type="number" class="modern-form-control" id="amount" formControlName="amount" placeholder="0.00" style="width: 70%;">
                </div>
              </div>
              <button type="submit" class="modern-btn modern-btn-primary w-100" [disabled]="!topupForm.valid || !memberControl.value">
                <i class="fas fa-plus-circle modern-btn-icon"></i> Bakiye Yükle
              </button>
            </form>
          </div>
        </div>
      </div>

      <!-- Bakiye Listesi -->
      <div class="col-md-8">
        <div class="modern-card slide-in-right">
          <div class="modern-card-header">
            <h5>Bakiye Listesi</h5>
            <div class="d-flex flex-column">
              <!-- Arama ve Filtre Satırı -->
              <div class="d-flex align-items-center justify-content-between mb-3">
                <div class="position-relative search-container">
                  <input type="text" class="modern-form-control search-input" placeholder="Üye ara..." [(ngModel)]="searchTerm" (input)="filterMembers()">
                  <i class="fas fa-search search-icon"></i>
                </div>
                <div class="filter-info">
                  <span class="filter-count">{{filteredMembersWithBalance.length}} üye</span>
                </div>
              </div>

              <!-- Filtre Butonları -->
              <div class="balance-filter-container">
                <div class="balance-filter-buttons">
                  <button
                    class="balance-filter-btn"
                    [class.active]="balanceFilter === 'all'"
                    (click)="filterByBalanceType('all')">
                    <div class="filter-btn-content">
                      <i class="fas fa-list filter-btn-icon"></i>
                      <span class="filter-btn-text">Tümü</span>
                      <span class="filter-btn-count">{{membersWithBalance.length}}</span>
                    </div>
                  </button>

                  <button
                    class="balance-filter-btn positive"
                    [class.active]="balanceFilter === 'positive'"
                    (click)="filterByBalanceType('positive')">
                    <div class="filter-btn-content">
                      <i class="fas fa-arrow-up filter-btn-icon"></i>
                      <span class="filter-btn-text">Pozitif Bakiye</span>
                      <span class="filter-btn-count">{{getPositiveBalanceCount()}}</span>
                    </div>
                  </button>

                  <button
                    class="balance-filter-btn negative"
                    [class.active]="balanceFilter === 'negative'"
                    (click)="filterByBalanceType('negative')">
                    <div class="filter-btn-content">
                      <i class="fas fa-arrow-down filter-btn-icon"></i>
                      <span class="filter-btn-text">Negatif Bakiye</span>
                      <span class="filter-btn-count">{{getNegativeBalanceCount()}}</span>
                    </div>
                  </button>
                </div>
              </div>
            </div>
          </div>
          <div class="modern-card-body">
            <div class="table-responsive">
              <table class="modern-table">
                <thead>
                  <tr>
                    <th (click)="sortBy('name')">
                      Adı <i class="fas" [ngClass]="getSortIcon('name')"></i>
                    </th>
                    <th (click)="sortBy('phoneNumber')">
                      Telefon <i class="fas" [ngClass]="getSortIcon('phoneNumber')"></i>
                    </th>
                    <th (click)="sortBy('balance')">
                      Bakiye <i class="fas" [ngClass]="getSortIcon('balance')"></i>
                    </th>
                    <th>İşlem</th>
                  </tr>
                </thead>
                <tbody>
                  <tr *ngFor="let member of filteredMembersWithBalance">
                    <td>{{member.name}}</td>
                    <td>{{member.phoneNumber}}</td>
                    <td>
                      <span [ngClass]="{'text-success': member.balance > 0, 'text-danger': member.balance < 0}">
                        {{member.balance | currency:'TRY':'symbol-narrow':'1.2-2'}}
                      </span>
                    </td>
                    <td>
                      <button class="modern-btn modern-btn-primary modern-btn-sm" (click)="openUpdateDialog(member)">
                        <i class="fas fa-edit"></i> Güncelle
                      </button>
                    </td>
                  </tr>
                  <tr *ngIf="filteredMembersWithBalance.length === 0">
                    <td colspan="4" class="text-center py-3">Bakiyeli üye bulunamadı</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
          <div class="modern-card-footer">
            <div class="d-flex justify-content-between align-items-center">
              <div>
                <span class="modern-badge modern-badge-primary">Toplam: {{filteredMembersWithBalance.length}} üye</span>
              </div>
              <nav *ngIf="totalPages > 1">
                <ul class="modern-pagination">
                  <li class="modern-page-item" [class.disabled]="currentPage === 1">
                    <a class="modern-page-link" (click)="changePage(currentPage - 1)" aria-label="Previous">
                      <span aria-hidden="true">&laquo;</span>
                    </a>
                  </li>
                  <li class="modern-page-item" *ngFor="let page of getPageNumbers()" [class.active]="page === currentPage">
                    <a class="modern-page-link" (click)="changePage(page)">{{page}}</a>
                  </li>
                  <li class="modern-page-item" [class.disabled]="currentPage === totalPages">
                    <a class="modern-page-link" (click)="changePage(currentPage + 1)" aria-label="Next">
                      <span aria-hidden="true">&raquo;</span>
                    </a>
                  </li>
                </ul>
              </nav>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
