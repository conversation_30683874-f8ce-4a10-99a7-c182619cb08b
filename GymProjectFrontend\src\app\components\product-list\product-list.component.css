/* Product List Component Styles */

/* Modern Sort Button */
.modern-sort-btn {
  background: linear-gradient(135deg, var(--secondary), var(--secondary-dark));
  border: none;
  border-radius: 12px;
  color: white;
  padding: 10px 16px;
  font-weight: 500;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  box-shadow: 0 4px 12px rgba(var(--secondary-rgb), 0.3);
  position: relative;
  overflow: hidden;
}

.modern-sort-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(var(--secondary-rgb), 0.4);
  background: linear-gradient(135deg, var(--secondary-dark), var(--secondary));
  color: white;
}

.modern-sort-btn:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(var(--secondary-rgb), 0.3);
  color: white;
}

.modern-sort-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.modern-sort-btn:hover::before {
  left: 100%;
}

.sort-text {
  font-size: 0.9rem;
  font-weight: 500;
}

.sort-badge {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  padding: 2px 8px;
  border-radius: 10px;
  font-size: 0.8rem;
  font-weight: 600;
  margin-left: 8px;
}

/* Modern Sort Dropdown */
.modern-sort-dropdown {
  border: none;
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
  padding: 8px;
  margin-top: 8px;
  background: white;
  min-width: 200px;
}

.modern-sort-item {
  border-radius: 8px;
  padding: 12px 16px;
  margin-bottom: 4px;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  color: var(--text-primary);
  text-decoration: none;
  position: relative;
  overflow: hidden;
}

.modern-sort-item:last-child {
  margin-bottom: 0;
}

.modern-sort-item:hover {
  background: var(--bg-secondary);
  color: var(--secondary);
  transform: translateX(4px);
  text-decoration: none;
}

.modern-sort-item.active {
  background: linear-gradient(135deg, var(--secondary), var(--secondary-dark));
  color: white;
  box-shadow: 0 4px 12px rgba(var(--secondary-rgb), 0.3);
}

.modern-sort-item.active:hover {
  transform: translateX(0);
  background: linear-gradient(135deg, var(--secondary-dark), var(--secondary));
  text-decoration: none;
  color: white;
}

/* Sort Dropdown Animation */
.modern-sort-dropdown {
  animation: sortDropdownFadeIn 0.3s ease;
}

@keyframes sortDropdownFadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}