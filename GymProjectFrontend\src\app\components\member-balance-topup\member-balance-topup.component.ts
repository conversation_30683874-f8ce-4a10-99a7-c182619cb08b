import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, FormControl, Validators } from '@angular/forms';
import { MemberService } from '../../services/member.service';
import { TransactionService } from '../../services/transaction.service';
import { ToastrService } from 'ngx-toastr';
import { Member } from '../../models/member';
import { Observable } from 'rxjs';
import { map, startWith } from 'rxjs/operators';
import { MatDialog } from '@angular/material/dialog';
import { UpdateBalanceDialogComponent } from '../update-balance-dialog/update-balance-dialog.component';

@Component({
    selector: 'app-member-balance-topup',
    templateUrl: './member-balance-topup.component.html',
    styleUrls: ['./member-balance-topup.component.css'],
    standalone: false
})
export class MemberBalanceTopupComponent implements OnInit {
  // Form controls
  topupForm: FormGroup;
  memberControl = new FormControl();
  
  // Data properties
  members: Member[] = [];
  filteredMembers: Observable<Member[]>;
  membersWithBalance: Member[] = [];
  filteredMembersWithBalance: Member[] = [];
  isLoading: boolean = false;
  
  // Search and filter properties
  searchTerm: string = '';
  balanceFilter: string = 'all'; // 'all', 'positive', 'negative'
  
  // Sorting properties
  sortColumn: string = 'name';
  sortDirection: 'asc' | 'desc' = 'asc';
  
  // Pagination properties
  currentPage: number = 1;
  itemsPerPage: number = 10;
  totalPages: number = 1;

  constructor(
    private fb: FormBuilder,
    private memberService: MemberService,
    private transactionService: TransactionService,
    private toastrService: ToastrService,
    private dialog: MatDialog
  ) {}

  ngOnInit(): void {
    this.createTopupForm();
    this.getMembers();
    
    this.filteredMembers = this.memberControl.valueChanges.pipe(
      startWith(''),
      map(value => typeof value === 'string' ? value : value?.name),
      map(name => name ? this._filterMembers(name) : this.members.slice())
    );
  }

  createTopupForm() {
    this.topupForm = this.fb.group({
      amount: ['', [Validators.required, Validators.min(0.01)]]
    });
  }

  getMembers() {
    this.isLoading = true;
    this.memberService.getMembers().subscribe(
      response => {
        this.members = response.data;
        this.updateMembersWithBalance();
        this.applyFiltersAndSort();
        this.isLoading = false;
      },
      error => {
        this.toastrService.error('Üye bilgileri yüklenirken bir hata oluştu', 'Hata');
        this.isLoading = false;
      }
    );
  }

  refreshData() {
    this.getMembers();
    this.toastrService.info('Veriler yenilendi', 'Bilgi');
  }

  updateMembersWithBalance() {
    this.membersWithBalance = this.members.filter(member => member.balance > 0 || member.balance < 0);
    this.applyFiltersAndSort();
  }

  // Statistics methods
  getTotalPositiveBalance(): number {
    return this.membersWithBalance
      .filter(member => member.balance > 0)
      .reduce((sum, member) => sum + member.balance, 0);
  }

  getTotalNegativeBalance(): number {
    return this.membersWithBalance
      .filter(member => member.balance < 0)
      .reduce((sum, member) => sum + member.balance, 0);
  }

  getNetBalance(): number {
    return this.membersWithBalance.reduce((sum, member) => sum + member.balance, 0);
  }

  getPositiveBalanceCount(): number {
    return this.membersWithBalance.filter(member => member.balance > 0).length;
  }

  getNegativeBalanceCount(): number {
    return this.membersWithBalance.filter(member => member.balance < 0).length;
  }

  // Display and filter methods
  displayMember(member: Member): string {
    return member && member.name ? `${member.name} - ${member.phoneNumber}` : '';
  }

  private _filterMembers(name: string): Member[] {
    const filterValue = name.toLowerCase();
    return this.members.filter(member => 
      member.name.toLowerCase().includes(filterValue) || 
      member.phoneNumber.includes(filterValue)
    );
  }

  filterMembers() {
    this.currentPage = 1;
    this.applyFiltersAndSort();
  }

  filterByBalanceType(type: string) {
    this.balanceFilter = type;
    this.currentPage = 1;
    this.applyFiltersAndSort();
  }

  applyFiltersAndSort() {
    // First apply search filter
    let filtered = this.membersWithBalance;
    
    if (this.searchTerm) {
      const search = this.searchTerm.toLowerCase();
      filtered = filtered.filter(member => 
        member.name.toLowerCase().includes(search) || 
        member.phoneNumber.includes(search)
      );
    }
    
    // Then apply balance type filter
    if (this.balanceFilter === 'positive') {
      filtered = filtered.filter(member => member.balance > 0);
    } else if (this.balanceFilter === 'negative') {
      filtered = filtered.filter(member => member.balance < 0);
    }
    
    // Apply sorting
    filtered = this.sortMembers(filtered);
    
    // Update pagination
    this.totalPages = Math.ceil(filtered.length / this.itemsPerPage);
    
    // Apply pagination
    const startIndex = (this.currentPage - 1) * this.itemsPerPage;
    this.filteredMembersWithBalance = filtered.slice(startIndex, startIndex + this.itemsPerPage);
  }

  // Sorting methods
  sortBy(column: string) {
    if (this.sortColumn === column) {
      // Toggle direction if clicking the same column
      this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';
    } else {
      // Default to ascending for a new column
      this.sortColumn = column;
      this.sortDirection = 'asc';
    }
    
    this.applyFiltersAndSort();
  }

  getSortIcon(column: string): string {
    if (this.sortColumn !== column) {
      return 'fa-sort';
    }
    return this.sortDirection === 'asc' ? 'fa-sort-up' : 'fa-sort-down';
  }

  sortMembers(members: Member[]): Member[] {
    return [...members].sort((a, b) => {
      let comparison = 0;
      
      if (this.sortColumn === 'name') {
        comparison = a.name.localeCompare(b.name);
      } else if (this.sortColumn === 'phoneNumber') {
        comparison = a.phoneNumber.localeCompare(b.phoneNumber);
      } else if (this.sortColumn === 'balance') {
        comparison = a.balance - b.balance;
      }
      
      return this.sortDirection === 'asc' ? comparison : -comparison;
    });
  }

  // Pagination methods
  changePage(page: number) {
    if (page < 1 || page > this.totalPages) {
      return;
    }
    
    this.currentPage = page;
    this.applyFiltersAndSort();
  }

  getPageNumbers(): number[] {
    const pages: number[] = [];
    const maxPagesToShow = 5;
    
    if (this.totalPages <= maxPagesToShow) {
      // Show all pages if there are few
      for (let i = 1; i <= this.totalPages; i++) {
        pages.push(i);
      }
    } else {
      // Show a window of pages around the current page
      let startPage = Math.max(1, this.currentPage - Math.floor(maxPagesToShow / 2));
      let endPage = startPage + maxPagesToShow - 1;
      
      if (endPage > this.totalPages) {
        endPage = this.totalPages;
        startPage = Math.max(1, endPage - maxPagesToShow + 1);
      }
      
      for (let i = startPage; i <= endPage; i++) {
        pages.push(i);
      }
    }
    
    return pages;
  }

  // Transaction methods
  topup() {
    if (this.topupForm.valid && this.memberControl.value) {
      this.isLoading = true;
      const selectedMember = this.memberControl.value;
      const topupData = {
        memberID: selectedMember.memberID,
        amount: this.topupForm.get('amount')?.value,
        transactionType: 'Bakiye Yükleme'
      };

      this.transactionService.addTransaction(topupData).subscribe(
        response => {
          this.toastrService.success('Bakiye yükleme başarılı', 'Başarılı');
          this.topupForm.reset({amount: ''});
          this.memberControl.reset();
          this.getMembers();
          this.isLoading = false;
        },
        error => {
          this.toastrService.error('Bakiye yükleme başarısız', 'Hata');
          this.isLoading = false;
        }
      );
    }
  }

  openUpdateDialog(member: Member) {
    const dialogRef = this.dialog.open(UpdateBalanceDialogComponent, {
      width: '400px',
      panelClass: 'modern-dialog',
      data: { member: member }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.getMembers();
      }
    });
  }
}